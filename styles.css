.flex-container-row{
    display: flex;
    flex-direction: row;
    gap: 10px;
}
.flex-container-column{
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 200px;
}
.flex-container-wrap{
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    gap: 10px;
    width: 240px;
    background-color: gray;
    justify-content: center;
    padding: 10px;
}
.flex-container-space{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    background-color: gray;
}
.flex-container-center{
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: gray;
    width: 400px;
    height: 400px;
}

.item-order-first {order: 1;}
.item-order-second {order: 2;}
.item-order-third {order: 3;}