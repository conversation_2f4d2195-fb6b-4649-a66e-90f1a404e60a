<!DOCTYPE html>
<html>
  <head>
    <title>Flexbox Lab Exercise</title>
    <link rel="stylesheet" href="styles.css" />
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #f9f9f9;
        margin: 20px;
      }

      #main-title {
        text-align: center;
        color: darkblue;
        margin-bottom: 20px;
      }

      .section-title {
        font-size: 20px;
        margin: 15px 0 10px;
      }

      .box {
        background: lightgray;
        border: 1px solid #333;
        padding: 20px;
        text-align: center;
      }
    </style>
  </head>
  <body>
    <h1 id="main-title">Flexbox Exercise</h1>

    <h2 class="section-title">First Container</h2>
    <div class="flex-container-row">
      <div class="box">1</div>
      <div class="box">2</div>
      <div class="box">3</div>
    </div>

    <h2 class="section-title">Second Container</h2>
    <div class="flex-container-column">
      <div class="box">A</div>
      <div class="box">B</div>
      <div class="box">C</div>
    </div>

    <h2 class="section-title">Third Container</h2>
    <div class="flex-container-wrap">
      <div class="box">1</div>
      <div class="box">2</div>
      <div class="box">3</div>
      <div class="box">4</div>
      <div class="box">5</div>
      <div class="box">6</div>
    </div>

    <h2 class="section-title">Fourth Container</h2>
    <div style="width: 75% !important" class="flex-container-space">
      <div class="box">A</div>
      <div class="box">B</div>
      <div class="box">C</div>
    </div>
    <br />
    <div style="width: 50% !important" class="flex-container-space">
      <div class="box">A</div>
      <div class="box">B</div>
      <div class="box">C</div>
    </div>
    <br />
    <div style="width: 25% !important" class="flex-container-space">
      <div class="box">A</div>
      <div class="box">B</div>
      <div class="box">C</div>
    </div>

    <!-- Align Items -->
    <h2 class="section-title">Fifth Container</h2>
    <div class="flex-container-center">
      <div class="box">Centered</div>
      <div class="box">Centered</div>
      <div class="box">Centered</div>
    </div>

    <!-- Flex Item: Order -->
    <h2 class="section-title">Sixth Container</h2>
    <div class="flex-container-row">
      <div class="box item-order-third">1</div>
      <div class="box item-order-third">1</div>
      <div class="box item-order-third">1</div>
      <div class="box item-order-first">2</div>
      <div class="box item-order-second">3</div>
    </div>
  </body>
</html>
